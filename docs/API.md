# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (Bearer Token)

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "success",
    "data": [],
    "meta": {
        "current_page": 1,
        "per_page": 10,
        "total": 100,
        "total_pages": 10
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "错误信息",
    "details": "详细错误描述"
}
```

## 前台API接口

### 1. 获取作品列表

**接口地址**: `GET /portfolios`

**请求体** (POST方式获取列表):
```json
{
    "page": 1,
    "per_page": 12,
    "filters": {
        "category_id": 1,
        "search": "关键词",
        "status": "published"
    }
}
```

**或使用简单GET方式**:
```
GET /portfolios (获取默认列表)
GET /portfolios/page/{page} (获取指定页)
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "title": "网站设计作品",
            "description": "这是一个响应式网站设计作品",
            "category_id": 1,
            "category": {
                "id": 1,
                "name": "网页设计"
            },
            "cover_image": "/uploads/portfolio_1_cover.jpg",
            "view_count": 156,
            "created_at": "2024-01-15T10:30:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 12,
        "total": 25,
        "total_pages": 3
    }
}
```

### 2. 获取作品详情

**接口地址**: `GET /portfolios/{id}`

**路径参数**:
- `id`: 作品ID

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "title": "网站设计作品",
        "description": "这是一个响应式网站设计作品的详细描述...",
        "category_id": 1,
        "category": {
            "id": 1,
            "name": "网页设计",
            "description": "网页设计相关作品"
        },
        "cover_image": "/uploads/portfolio_1_cover.jpg",
        "view_count": 157,
        "images": [
            {
                "id": 1,
                "image_url": "/uploads/portfolio_1_img1.jpg",
                "image_name": "首页设计",
                "sort_order": 1
            },
            {
                "id": 2,
                "image_url": "/uploads/portfolio_1_img2.jpg",
                "image_name": "内页设计",
                "sort_order": 2
            }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}
```

### 3. 增加作品浏览量

**接口地址**: `POST /portfolios/{id}/view`

**路径参数**:
- `id`: 作品ID

**响应示例**:
```json
{
    "code": 200,
    "message": "浏览量已更新"
}
```

### 4. 获取分类列表

**接口地址**: `GET /categories`

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "网页设计",
            "description": "网页设计相关作品",
            "sort_order": 1
        },
        {
            "id": 2,
            "name": "移动应用",
            "description": "移动应用设计作品",
            "sort_order": 2
        }
    ]
}
```

### 5. 获取分类下的作品

**接口地址**: `GET /categories/{id}/portfolios`

**路径参数**:
- `id`: 分类ID

**响应示例**: (同作品列表格式)

### 6. 搜索作品

**接口地址**: `POST /portfolios/search`

**请求体**:
```json
{
    "keyword": "搜索关键词",
    "page": 1,
    "per_page": 12
}
```

**响应示例**: (同作品列表格式)

## 后台管理API接口

### 认证相关

#### 1. 管理员登录

**接口地址**: `POST /admin/login`

**请求参数**:
```json
{
    "username": "admin",
    "password": "password123"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>"
        }
    }
}
```

#### 2. 管理员登出

**接口地址**: `POST /admin/logout`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
    "code": 200,
    "message": "登出成功"
}
```

### 作品管理

#### 1. 获取作品列表(管理)

**接口地址**: `GET /admin/portfolios` (获取默认列表)

**请求头**: `Authorization: Bearer {token}`

**响应示例**: (同前台作品列表格式)

#### 1.1. 管理员搜索作品

**接口地址**: `POST /admin/portfolios/search`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
{
    "page": 1,
    "per_page": 10,
    "filters": {
        "category_id": 1,
        "status": "published",
        "search": "关键词"
    }
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "title": "网站设计作品",
            "description": "这是一个响应式网站设计作品",
            "category_id": 1,
            "category": {
                "id": 1,
                "name": "网页设计"
            },
            "cover_image": "/uploads/portfolio_1_cover.jpg",
            "view_count": 156,
            "status": "published",
            "sort_order": 1,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 10,
        "total": 25,
        "total_pages": 3
    }
}
```

#### 2. 创建作品

**接口地址**: `POST /admin/portfolios`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "title": "新作品标题",
    "description": "作品描述",
    "category_id": 1,
    "cover_image": "/uploads/cover.jpg",
    "status": "draft",
    "sort_order": 1,
    "images": [
        {
            "image_url": "/uploads/img1.jpg",
            "image_name": "图片1",
            "sort_order": 1
        },
        {
            "image_url": "/uploads/img2.jpg",
            "image_name": "图片2",
            "sort_order": 2
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "作品创建成功",
    "data": {
        "id": 26,
        "title": "新作品标题",
        "description": "作品描述",
        "category_id": 1,
        "cover_image": "/uploads/cover.jpg",
        "view_count": 0,
        "status": "draft",
        "sort_order": 1,
        "created_at": "2024-01-20T15:30:00Z",
        "updated_at": "2024-01-20T15:30:00Z"
    }
}
```

#### 3. 更新作品

**接口地址**: `PUT /admin/portfolios/{id}`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
- `id`: 作品ID

**请求参数**: (同创建作品)

**响应示例**:
```json
{
    "code": 200,
    "message": "作品更新成功",
    "data": {
        "id": 1,
        "title": "更新后的标题",
        "description": "更新后的描述",
        "category_id": 1,
        "cover_image": "/uploads/new_cover.jpg",
        "view_count": 156,
        "status": "published",
        "sort_order": 1,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-20T16:00:00Z"
    }
}
```

#### 4. 删除作品

**接口地址**: `DELETE /admin/portfolios/{id}`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
- `id`: 作品ID

**响应示例**:
```json
{
    "code": 200,
    "message": "作品删除成功"
}
```

### 文件上传

#### 1. 上传图片

**接口地址**: `POST /admin/upload`

**请求头**: 
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 图片文件 (form-data)
- `type`: 上传类型 (cover/gallery) (可选)

**响应示例**:
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "url": "/uploads/20240120/image_123456.jpg",
        "filename": "image_123456.jpg",
        "size": 1024000,
        "type": "image/jpeg"
    }
}
```

### 分类管理

#### 1. 获取分类列表(管理)

**接口地址**: `GET /admin/categories`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "网页设计",
            "description": "网页设计相关作品",
            "sort_order": 1,
            "portfolio_count": 15,
            "created_at": "2024-01-10T10:00:00Z"
        }
    ]
}
```

#### 2. 创建分类

**接口地址**: `POST /admin/categories`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "name": "新分类",
    "description": "分类描述",
    "sort_order": 3
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "分类创建成功",
    "data": {
        "id": 3,
        "name": "新分类",
        "description": "分类描述",
        "sort_order": 3,
        "created_at": "2024-01-20T16:30:00Z"
    }
}
```

#### 3. 更新分类

**接口地址**: `PUT /admin/categories/{id}`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
- `id`: 分类ID

**请求参数**: (同创建分类)

**响应示例**:
```json
{
    "code": 200,
    "message": "分类更新成功",
    "data": {
        "id": 1,
        "name": "更新后的分类名",
        "description": "更新后的描述",
        "sort_order": 1,
        "created_at": "2024-01-10T10:00:00Z",
        "updated_at": "2024-01-20T17:00:00Z"
    }
}
```

#### 4. 删除分类

**接口地址**: `DELETE /admin/categories/{id}`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
- `id`: 分类ID

**响应示例**:
```json
{
    "code": 200,
    "message": "分类删除成功"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 请求限制

- 文件上传大小限制: 10MB
- 支持的图片格式: JPG, JPEG, PNG, GIF
- API请求频率限制: 100次/分钟/IP
- JWT Token有效期: 24小时

## 使用示例

### JavaScript (Fetch API)

```javascript
// 获取作品列表
async function getPortfolios(page = 1, categoryId = null) {
    const params = new URLSearchParams({
        page: page,
        per_page: 12
    });
    
    if (categoryId) {
        params.append('category_id', categoryId);
    }
    
    const response = await fetch(`/api/portfolios?${params}`);
    const data = await response.json();
    return data;
}

// 管理员登录
async function adminLogin(username, password) {
    const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    if (data.code === 200) {
        localStorage.setItem('token', data.data.token);
    }
    return data;
}

// 创建作品 (需要认证)
async function createPortfolio(portfolioData) {
    const token = localStorage.getItem('token');
    
    const response = await fetch('/api/admin/portfolios', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(portfolioData)
    });
    
    return await response.json();
}

// 上传图片
async function uploadImage(file) {
    const token = localStorage.getItem('token');
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/admin/upload', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        },
        body: formData
    });
    
    return await response.json();
}
```

### cURL示例

```bash
# 获取作品列表
curl -X GET "http://localhost:8080/api/portfolios"

# 搜索作品
curl -X POST "http://localhost:8080/api/portfolios/search" \
  -H "Content-Type: application/json" \
  -d '{"keyword":"设计","page":1,"per_page":12}'

# 获取分类下的作品
curl -X GET "http://localhost:8080/api/categories/1/portfolios"

# 管理员登录
curl -X POST "http://localhost:8080/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# 创建作品 (需要token)
curl -X POST "http://localhost:8080/api/admin/portfolios" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "新作品",
    "description": "作品描述",
    "category_id": 1,
    "status": "published"
  }'

# 上传图片
curl -X POST "http://localhost:8080/api/admin/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/image.jpg"
```

这个API文档提供了完整的接口说明，包括请求参数、响应格式和使用示例，方便前端开发和接口测试。
